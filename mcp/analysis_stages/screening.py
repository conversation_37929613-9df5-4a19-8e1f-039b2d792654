#!/usr/bin/env python3
"""
Screening Stage

First stage in the analysis pipeline that quickly assesses if a filing
is worth analyzing in more detail. Enhanced with tool orchestration capabilities.
"""

import json
import logging
import re
from typing import Dict, List, Any, Optional

from .base import BaseAnalysisStage

logger = logging.getLogger(__name__)


class ScreeningStage(BaseAnalysisStage):
    """
    Screening stage for quick assessment of filings.
    Enhanced with tool orchestration capabilities.
    """

    def __init__(self, screening_threshold: float = 0.3, tool_orchestrator=None):
        """
        Initialize the screening stage.

        Args:
            screening_threshold: Threshold for proceeding to detailed analysis
            tool_orchestrator: Tool orchestrator for intelligent tool selection
        """
        super().__init__(name="screening")
        self.screening_threshold = screening_threshold
        self.tool_orchestrator = tool_orchestrator
        self.prompt_template = """
You are an expert financial analyst specializing in private market investments and SEC Form D filings.
Perform an initial screening of this Form D filing to determine if it warrants detailed analysis.

# Filing Information
{filing_info}

# Screening Task
Quickly assess this filing based on:
1. Offering amount (larger offerings are generally more significant)
2. Industry sector (focus on tech, healthcare, energy, financial services)
3. Notable investors or executives (if mentioned)
4. Unusual or standout features

# Response Format
Respond in JSON format with the following structure:
{{
  "initial_score": float,  // 0.0-1.0 where 1.0 is highest potential significance
  "reasoning": "string",   // Brief explanation of your score (1-2 sentences)
  "key_points": [          // List of 2-3 key points that influenced your decision
    "string",
    "string"
  ]
}}
"""

    def create_prompt(self, entry: Dict[str, Any], context: Optional[Dict[str, Any]] = None) -> str:
        """
        Create a screening prompt for a filing entry.

        Args:
            entry: Filing entry to analyze
            context: Optional context (not used in screening stage)

        Returns:
            Formatted prompt string
        """
        # Format filing information (simplified for screening)
        filing_info = f"""
Title: {entry.get('title', 'Unknown')}
Filing Date: {entry.get('filing_date', 'Unknown')}
Offering Amount: ${entry.get('offering_amount', 0):,.2f}
Industry: {entry.get('industry', 'Unknown')}
Summary: {entry.get('summary', 'No summary available')}
"""

        # Add SEC API data if available (simplified)
        if entry.get('sec_api_enriched', False) and 'company_info' in entry:
            company_info = entry.get('company_info', {})
            filing_info += f"""
Company Name: {company_info.get('name', 'Unknown')}
SIC Code: {company_info.get('sic', 'Unknown')} - {company_info.get('sic_description', 'Unknown')}
"""

        # Format the full prompt
        prompt = self.prompt_template.format(
            filing_info=filing_info
        )

        return prompt

    def parse_output(self, llm_output: str) -> Dict[str, Any]:
        """
        Parse LLM output for the screening stage.

        Args:
            llm_output: Raw output from LLM

        Returns:
            Parsed output as dictionary
        """
        # Default values
        parsed = {
            "initial_score": 0.0,
            "reasoning": "Failed to parse LLM output",
            "key_points": []
        }

        try:
            # Try to extract JSON from the output
            json_match = re.search(r'```json\s*(.*?)\s*```', llm_output, re.DOTALL)
            if json_match:
                json_str = json_match.group(1)
            else:
                # Try to find JSON without code block markers
                json_match = re.search(r'({.*})', llm_output, re.DOTALL)
                if json_match:
                    json_str = json_match.group(1)
                else:
                    # Use the whole output as a last resort
                    json_str = llm_output

            # Parse the JSON
            parsed_json = json.loads(json_str)

            # Extract fields
            if "initial_score" in parsed_json:
                score = float(parsed_json["initial_score"])
                # Ensure score is in valid range
                parsed["initial_score"] = max(0.0, min(1.0, score))

            if "reasoning" in parsed_json:
                parsed["reasoning"] = parsed_json["reasoning"]

            if "key_points" in parsed_json:
                parsed["key_points"] = parsed_json["key_points"]

        except Exception as e:
            logging.error(f"Error parsing screening output: {e}")
            logging.debug(f"Raw LLM output: {llm_output}")

        return parsed

    def should_proceed(self, parsed_output: Dict[str, Any]) -> bool:
        """
        Determine if the filing should proceed to detailed analysis.

        Args:
            parsed_output: Parsed output from screening stage

        Returns:
            Boolean indicating whether to proceed
        """
        # Check if initial score exceeds threshold
        return parsed_output.get("initial_score", 0.0) >= self.screening_threshold

    def process_with_tools(self, entry: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process entry with intelligent tool selection and execution.

        Args:
            entry: Filing entry to process

        Returns:
            Enhanced entry with tool results
        """
        if not self.tool_orchestrator:
            logger.warning("No tool orchestrator available for screening stage")
            return entry

        try:
            # Let LLM decide which tools to use for screening
            tool_decisions = self.tool_orchestrator.select_tools(
                filing_entry=entry,
                analysis_stage="screening",
                previous_results=None
            )

            # Execute selected tools
            tool_results = self.tool_orchestrator.execute_tool_chain(
                tool_decisions=tool_decisions,
                filing_entry=entry
            )

            # Add tool results to entry
            if tool_results:
                entry["screening_tool_results"] = tool_results
                entry["tool_decisions"] = tool_decisions

                logger.info(f"Screening stage used {len(tool_results)} tools: {list(tool_results.keys())}")

            return entry

        except Exception as e:
            logger.error(f"Error in screening tool orchestration: {e}")
            return entry

#!/usr/bin/env python3
"""
Detailed Analysis Stage

Second stage in the analysis pipeline that performs in-depth analysis
of filings that passed the screening stage.
"""

import json
import logging
import re
from typing import Dict, List, Any, Optional

from .base import BaseAnalysisStage

logger = logging.getLogger(__name__)


class DetailedAnalysisStage(BaseAnalysisStage):
    """
    Detailed analysis stage for in-depth assessment of filings.
    Enhanced with tool orchestration capabilities.
    """

    def __init__(self, relevance_threshold: float = 0.5, tool_orchestrator=None):
        """
        Initialize the detailed analysis stage.

        Args:
            relevance_threshold: Threshold for proceeding to action generation
            tool_orchestrator: Tool orchestrator for intelligent tool selection
        """
        super().__init__(name="detailed_analysis")
        self.relevance_threshold = relevance_threshold
        self.tool_orchestrator = tool_orchestrator
        self.prompt_template = """
You are an expert financial analyst specializing in private market investments and SEC Form D filings.
Perform a detailed analysis of this Form D filing that passed initial screening.

# Filing Information
{filing_info}

# Historical Context
{historical_context}

# News Context
{news_context}

# Initial Screening
{screening_results}

# Analysis Task
Perform a detailed analysis considering:
1. Company background and leadership team
2. Investment terms and structure
3. Market positioning and competitive landscape
4. Historical context and similar filings
5. News and media coverage
6. Potential impact and significance

# Chain of Thought
Think step-by-step through your analysis:
1. First, analyze the basic filing details and company information
2. Next, consider the historical context and similar filings
3. Then, incorporate news and media coverage
4. Finally, synthesize all information to determine relevance and significance

# Response Format
Respond in JSON format with the following structure:
{{
  "relevance_score": float,  // 0.0-1.0 where 1.0 is highest relevance
  "summary": "string",       // 2-3 sentence summary
  "analysis": {{
    "company_analysis": "string",
    "investment_analysis": "string",
    "market_analysis": "string",
    "risk_factors": ["string", "string"],
    "opportunities": ["string", "string"]
  }},
  "reasoning_chain": [       // Step-by-step reasoning process
    "string",
    "string",
    "string"
  ]
}}
"""

    def create_prompt(self, entry: Dict[str, Any], context: Optional[Dict[str, Any]] = None) -> str:
        """
        Create a detailed analysis prompt for a filing entry.

        Args:
            entry: Filing entry to analyze
            context: Context from screening stage

        Returns:
            Formatted prompt string
        """
        # Format filing information
        filing_info = f"""
Title: {entry.get('title', 'Unknown')}
Filing Date: {entry.get('filing_date', 'Unknown')}
Offering Amount: ${entry.get('offering_amount', 0):,.2f}
Industry: {entry.get('industry', 'Unknown')}
Summary: {entry.get('summary', 'No summary available')}
Link: {entry.get('link', 'No link available')}
"""

        # Add SEC API data if available
        if entry.get('sec_api_enriched', False) and 'company_info' in entry:
            company_info = entry.get('company_info', {})
            filing_info += f"""
Company Information (SEC API):
Company Name: {company_info.get('name', 'Unknown')}
SIC Code: {company_info.get('sic', 'Unknown')} - {company_info.get('sic_description', 'Unknown')}
State of Incorporation: {company_info.get('state_of_incorporation', 'Unknown')}
Total SEC Filings: {company_info.get('filing_count', 0)}
"""

        # Format historical context
        similar_filings = entry.get('similar_filings', [])
        if similar_filings:
            historical_context = f"""
Found {len(similar_filings)} similar historical filings.
Average Similarity Distance: {entry.get('avg_similarity_distance', 0.0):.4f}
Same Industry Count: {entry.get('same_industry_count', 0)}
Offering Amount Percentile: {entry.get('amount_percentile', 0.0):.2f}

Top Similar Filings:
"""
            # Add details for up to 3 similar filings
            for i, filing in enumerate(similar_filings[:3]):
                metadata = filing.get('metadata', {})
                historical_context += f"""
{i+1}. Issuer: {metadata.get('issuer_name', 'Unknown')}
   Filing Date: {metadata.get('filing_date', 'Unknown')}
   Offering Amount: ${float(metadata.get('offering_amount', 0)):,.2f}
   Distance: {filing.get('distance', 0.0):.4f}
"""
        else:
            historical_context = "No similar historical filings found."

        # Format news context
        news_items = entry.get('news_context', [])
        if news_items:
            news_context = f"""
Found {len(news_items)} news articles related to this filing.

Top News Articles:
"""
            # Add details for up to 3 news articles
            for i, news in enumerate(news_items[:3]):
                news_context += f"""
{i+1}. Title: {news.get('title', 'Unknown')}
   Source: {news.get('source', 'Unknown')}
   Date: {news.get('date', 'Unknown')[:10]}
   Snippet: {news.get('snippet', 'No snippet available')}
"""
        else:
            news_context = "No related news articles found."

        # Format screening results
        if context and "initial_score" in context:
            screening_results = f"""
Initial Screening Score: {context.get('initial_score', 0.0):.2f}
Reasoning: {context.get('reasoning', 'No reasoning provided')}

Key Points:
"""
            for i, point in enumerate(context.get('key_points', [])):
                screening_results += f"{i+1}. {point}\n"
        else:
            screening_results = "No screening results available."

        # Format the full prompt
        prompt = self.prompt_template.format(
            filing_info=filing_info,
            historical_context=historical_context,
            news_context=news_context,
            screening_results=screening_results
        )

        return prompt

    def parse_output(self, llm_output: str) -> Dict[str, Any]:
        """
        Parse LLM output for the detailed analysis stage.

        Args:
            llm_output: Raw output from LLM

        Returns:
            Parsed output as dictionary
        """
        # Default values
        parsed = {
            "relevance_score": 0.0,
            "summary": "Failed to parse LLM output",
            "analysis": {
                "company_analysis": "",
                "investment_analysis": "",
                "market_analysis": "",
                "risk_factors": [],
                "opportunities": []
            },
            "reasoning_chain": []
        }

        try:
            # Try to extract JSON from the output
            json_match = re.search(r'```json\s*(.*?)\s*```', llm_output, re.DOTALL)
            if json_match:
                json_str = json_match.group(1)
            else:
                # Try to find JSON without code block markers
                json_match = re.search(r'({.*})', llm_output, re.DOTALL)
                if json_match:
                    json_str = json_match.group(1)
                else:
                    # Use the whole output as a last resort
                    json_str = llm_output

            # Parse the JSON
            parsed_json = json.loads(json_str)

            # Extract fields
            if "relevance_score" in parsed_json:
                score = float(parsed_json["relevance_score"])
                # Ensure score is in valid range
                parsed["relevance_score"] = max(0.0, min(1.0, score))

            if "summary" in parsed_json:
                parsed["summary"] = parsed_json["summary"]

            if "analysis" in parsed_json:
                parsed["analysis"] = parsed_json["analysis"]

            if "reasoning_chain" in parsed_json:
                parsed["reasoning_chain"] = parsed_json["reasoning_chain"]

        except Exception as e:
            logging.error(f"Error parsing detailed analysis output: {e}")
            logging.debug(f"Raw LLM output: {llm_output}")

        return parsed

    def should_proceed(self, parsed_output: Dict[str, Any]) -> bool:
        """
        Determine if the filing should proceed to action generation.

        Args:
            parsed_output: Parsed output from detailed analysis stage

        Returns:
            Boolean indicating whether to proceed
        """
        # Check if relevance score exceeds threshold
        return parsed_output.get("relevance_score", 0.0) >= self.relevance_threshold

    def process_with_tools(self, entry: Dict[str, Any], screening_results: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Process entry with intelligent tool selection and execution.

        Args:
            entry: Filing entry to process
            screening_results: Results from screening stage

        Returns:
            Enhanced entry with tool results
        """
        if not self.tool_orchestrator:
            logger.warning("No tool orchestrator available for detailed analysis stage")
            return entry

        try:
            # Let LLM decide which tools to use for detailed analysis
            tool_decisions = self.tool_orchestrator.select_tools(
                filing_entry=entry,
                analysis_stage="detailed_analysis",
                previous_results=screening_results
            )

            # Execute selected tools
            tool_results = self.tool_orchestrator.execute_tool_chain(
                tool_decisions=tool_decisions,
                filing_entry=entry
            )

            # Add tool results to entry
            if tool_results:
                entry["detailed_analysis_tool_results"] = tool_results
                entry["detailed_tool_decisions"] = tool_decisions

                logger.info(f"Detailed analysis stage used {len(tool_results)} tools: {list(tool_results.keys())}")

            return entry

        except Exception as e:
            logger.error(f"Error in detailed analysis tool orchestration: {e}")
            return entry

#!/usr/bin/env python3
"""
Action Generation Stage

Final stage in the analysis pipeline that generates actionable insights
and recommendations for filings that passed detailed analysis.
"""

import json
import logging
import re
from typing import Dict, List, Any, Optional

from .base import BaseAnalysisStage

logger = logging.getLogger(__name__)


class ActionGenerationStage(BaseAnalysisStage):
    """
    Action generation stage for creating actionable insights.
    Enhanced with tool orchestration capabilities.
    """

    def __init__(self, email_threshold: float = 0.7, tool_orchestrator=None):
        """
        Initialize the action generation stage.

        Args:
            email_threshold: Threshold for including email draft
            tool_orchestrator: Tool orchestrator for intelligent tool selection
        """
        super().__init__(name="action_generation")
        self.email_threshold = email_threshold
        self.tool_orchestrator = tool_orchestrator
        self.prompt_template = """
You are an expert financial analyst specializing in private market investments and SEC Form D filings.
Generate actionable insights and recommendations for this high-relevance Form D filing.

# Filing Information
{filing_info}

# Detailed Analysis
{detailed_analysis}

# Action Generation Task
Based on the detailed analysis, generate:
1. Key actionable insights
2. Specific recommendations for investors or stakeholders
3. A concise email draft summarizing the filing and its significance (if relevance score > {email_threshold})
4. Follow-up questions that would provide valuable additional information

# Response Format
Respond in JSON format with the following structure:
{{
  "actionable_insights": [
    "string",
    "string"
  ],
  "recommendations": [
    "string",
    "string"
  ],
  "email_draft": "string",  // Concise email content (if relevance_score > {email_threshold})
  "follow_up_questions": [
    "string",
    "string"
  ]
}}
"""

    def create_prompt(self, entry: Dict[str, Any], context: Optional[Dict[str, Any]] = None) -> str:
        """
        Create an action generation prompt for a filing entry.

        Args:
            entry: Filing entry to analyze
            context: Context from detailed analysis stage

        Returns:
            Formatted prompt string
        """
        # Format filing information (simplified)
        filing_info = f"""
Title: {entry.get('title', 'Unknown')}
Filing Date: {entry.get('filing_date', 'Unknown')}
Offering Amount: ${entry.get('offering_amount', 0):,.2f}
Industry: {entry.get('industry', 'Unknown')}
Link: {entry.get('link', 'No link available')}
"""

        # Format detailed analysis
        if context:
            detailed_analysis = f"""
Relevance Score: {context.get('relevance_score', 0.0):.2f}
Summary: {context.get('summary', 'No summary available')}

Company Analysis:
{context.get('analysis', {}).get('company_analysis', 'No company analysis available')}

Investment Analysis:
{context.get('analysis', {}).get('investment_analysis', 'No investment analysis available')}

Market Analysis:
{context.get('analysis', {}).get('market_analysis', 'No market analysis available')}

Risk Factors:
"""
            for i, risk in enumerate(context.get('analysis', {}).get('risk_factors', [])):
                detailed_analysis += f"{i+1}. {risk}\n"

            detailed_analysis += "\nOpportunities:\n"
            for i, opportunity in enumerate(context.get('analysis', {}).get('opportunities', [])):
                detailed_analysis += f"{i+1}. {opportunity}\n"

            # Include reasoning chain if available
            if context.get('reasoning_chain'):
                detailed_analysis += "\nReasoning Chain:\n"
                for i, step in enumerate(context.get('reasoning_chain', [])):
                    detailed_analysis += f"{i+1}. {step}\n"
        else:
            detailed_analysis = "No detailed analysis available."

        # Format the full prompt
        prompt = self.prompt_template.format(
            filing_info=filing_info,
            detailed_analysis=detailed_analysis,
            email_threshold=self.email_threshold
        )

        return prompt

    def parse_output(self, llm_output: str) -> Dict[str, Any]:
        """
        Parse LLM output for the action generation stage.

        Args:
            llm_output: Raw output from LLM

        Returns:
            Parsed output as dictionary
        """
        # Default values
        parsed = {
            "actionable_insights": [],
            "recommendations": [],
            "email_draft": "",
            "follow_up_questions": []
        }

        try:
            # Try to extract JSON from the output
            json_match = re.search(r'```json\s*(.*?)\s*```', llm_output, re.DOTALL)
            if json_match:
                json_str = json_match.group(1)
            else:
                # Try to find JSON without code block markers
                json_match = re.search(r'({.*})', llm_output, re.DOTALL)
                if json_match:
                    json_str = json_match.group(1)
                else:
                    # Use the whole output as a last resort
                    json_str = llm_output

            # Parse the JSON
            parsed_json = json.loads(json_str)

            # Extract fields
            if "actionable_insights" in parsed_json:
                parsed["actionable_insights"] = parsed_json["actionable_insights"]

            if "recommendations" in parsed_json:
                parsed["recommendations"] = parsed_json["recommendations"]

            if "email_draft" in parsed_json:
                parsed["email_draft"] = parsed_json["email_draft"]

            if "follow_up_questions" in parsed_json:
                parsed["follow_up_questions"] = parsed_json["follow_up_questions"]

        except Exception as e:
            logging.error(f"Error parsing action generation output: {e}")
            logging.debug(f"Raw LLM output: {llm_output}")

        return parsed

    def should_proceed(self, parsed_output: Dict[str, Any]) -> bool:
        """
        Determine if further processing is needed.
        For action generation (final stage), always return False.

        Args:
            parsed_output: Parsed output from action generation stage

        Returns:
            Always False (end of pipeline)
        """
        # This is the final stage, so always return False
        return False

    def process_with_tools(self, entry: Dict[str, Any], detailed_results: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Process entry with intelligent tool selection and execution for action generation.

        Args:
            entry: Filing entry to process
            detailed_results: Results from detailed analysis stage

        Returns:
            Enhanced entry with tool results and actions
        """
        if not self.tool_orchestrator:
            logger.warning("No tool orchestrator available for action generation stage")
            return entry

        try:
            # Let LLM decide which tools to use for action generation
            tool_decisions = self.tool_orchestrator.select_tools(
                filing_entry=entry,
                analysis_stage="action_generation",
                previous_results=detailed_results
            )

            # Execute selected tools
            tool_results = self.tool_orchestrator.execute_tool_chain(
                tool_decisions=tool_decisions,
                filing_entry=entry
            )

            # Add tool results to entry
            if tool_results:
                entry["action_generation_tool_results"] = tool_results
                entry["action_tool_decisions"] = tool_decisions

                # Check if any actions were actually executed (e.g., emails sent, issues created)
                actions_executed = []
                for tool_name, result in tool_results.items():
                    if tool_name == "microsoft365_mcp_server" and "result" in result:
                        actions_executed.append("email_sent")
                    elif tool_name == "github_mcp_server" and "result" in result:
                        actions_executed.append("github_issue_created")

                if actions_executed:
                    entry["actions_executed"] = actions_executed
                    logger.info(f"Action generation executed: {actions_executed}")

                logger.info(f"Action generation stage used {len(tool_results)} tools: {list(tool_results.keys())}")

            return entry

        except Exception as e:
            logger.error(f"Error in action generation tool orchestration: {e}")
            return entry

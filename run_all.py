
#!/usr/bin/env python3
"""
run_all.py

Orchestrates the full Private-Market Heat-Map pipeline using the Model Control Point (MCP):
1. Fetch Atom feed (latest Form-D summaries)
2. Save feed entries to JSONL
3. Fetch bulk ZIPs on-demand for historical context
4. Embed both feed and bulk entries into the vector store
5. Process entries through the MCP for analysis and scoring
6. Generate visualizations for relevant filings
7. Send email alerts for high-relevance filings
"""

import logging
import sys
import os
import datetime as dt
from pathlib import Path
import argparse
import time

# Import original pipeline components
from ingest.form_d_feed import fetch_feed, save_feed, poll_feed
from ingest.zip_on_demand import main as fetch_zips
from ingest.process_quarterly_zips import process_all_quarterly_zips
from vector_store.embed import upsert_feed_entries, upsert_bulk_entries
from emailer.sendgrid_client import send_email

# Import new MCP components
from mcp import ModelControlPoint

# Configure logging
logs_dir = Path("logs")
logs_dir.mkdir(exist_ok=True)

logging.basicConfig(
    filename='logs/run_all.log',
    level=logging.INFO,
    format='%(asctime)s %(levelname)s %(message)s'
)

def run_all(use_mcp=True, relevance_threshold=0.7, email_threshold=0.8, screening_threshold=0.3,
         skip_zip=False, use_multi_stage=True, use_prompt_evaluation=True, use_specialized_analysis=True):
    """
    Run the full pipeline with MCP integration.

    Args:
        use_mcp: Whether to use the Model Control Point (vs. legacy pipeline)
        relevance_threshold: Threshold for considering a filing relevant
        email_threshold: Threshold for triggering email alerts
        screening_threshold: Threshold for proceeding from screening to detailed analysis
        skip_zip: Whether to skip ZIP downloads and use placeholders
        use_multi_stage: Whether to use multi-stage analysis pipeline
        use_prompt_evaluation: Whether to use prompt evaluation and improvement
        use_specialized_analysis: Whether to use specialized industry/offering analysis
    """
    # Track success/failure of each step
    step_results = {}
    entries = None
    processed_entries = None

    # STEP 1: Fetch & save feed
    try:
        logging.info("STEP 1: fetch_feed")
        entries = fetch_feed()
        if not entries:
            logging.warning("No entries found in feed")
        save_feed(entries)
        logging.info("STEP 1 COMPLETE")
        step_results["step1"] = True
    except Exception:
        logging.exception("Error in STEP 1")
        step_results["step1"] = False
        # Continue execution instead of exiting

    # STEP 2a: Fetch on-demand ZIPs
    try:
        if skip_zip:
            logging.info("STEP 2a: Skipped ZIP fetch (--skip-zip flag)")
            step_results["step2a"] = True
        elif entries:  # Only proceed if we have entries
            logging.info("STEP 2a: fetch_zips")
            fetch_zips(top_n=10, skip_zip=skip_zip)
            logging.info("STEP 2a COMPLETE")
            step_results["step2a"] = True
        else:
            logging.warning("STEP 2a: Skipped (no entries from step 1)")
            step_results["step2a"] = False
    except Exception:
        logging.exception("Error in STEP 2a")
        step_results["step2a"] = False
        # Continue execution instead of exiting

    # STEP 2b: Process quarterly ZIP files (OPTIMIZED - Skip if data exists in Supabase)
    try:
        # Check if we have data in Supabase database
        from db.supabase_manager import SupabaseDatabaseManager
        try:
            db = SupabaseDatabaseManager()
            stats = db.get_stats()
            filing_count = stats.get('form_d_filings_count', 0)
            db.close()

            if filing_count > 100000:  # We have substantial data (137K+ filings)
                logging.info(f"STEP 2b: Skipped quarterly ZIP processing - {filing_count:,} filings already in Supabase database")
                step_results["step2b"] = True
            else:
                logging.info("STEP 2b: process_quarterly_zips")
                quarterly_jsonl_files = process_all_quarterly_zips(data_dir="data/raw")
                if quarterly_jsonl_files:
                    logging.info(f"STEP 2b COMPLETE - Processed {len(quarterly_jsonl_files)} quarterly ZIP files")
                    step_results["step2b"] = True
                else:
                    logging.warning("STEP 2b: No quarterly ZIP files processed")
                    step_results["step2b"] = True  # Still mark as success since this is optional
        except Exception as db_error:
            logging.warning(f"Could not check Supabase database: {db_error}")
            # Fallback to processing ZIPs
            logging.info("STEP 2b: process_quarterly_zips (fallback)")
            quarterly_jsonl_files = process_all_quarterly_zips(data_dir="data/raw")
            if quarterly_jsonl_files:
                logging.info(f"STEP 2b COMPLETE - Processed {len(quarterly_jsonl_files)} quarterly ZIP files")
                step_results["step2b"] = True
            else:
                logging.warning("STEP 2b: No quarterly ZIP files processed")
                step_results["step2b"] = True
    except Exception:
        logging.exception("Error in STEP 2b")
        step_results["step2b"] = False
        # Continue execution instead of exiting

    # STEP 3a: Embed feed entries
    try:
        if entries:  # Only proceed if we have entries
            logging.info("STEP 3a: upsert_feed_entries")
            upsert_feed_entries(entries)
            logging.info("STEP 3a COMPLETE")
            step_results["step3a"] = True
        else:
            logging.warning("STEP 3a: Skipped (no entries from step 1)")
            step_results["step3a"] = False
    except Exception:
        logging.exception("Error in STEP 3a")
        step_results["step3a"] = False
        # Continue execution instead of exiting

    # STEP 3b: Embed bulk entries (OPTIMIZED - Skip since we use database for historical context)
    try:
        # Check if we already have substantial embeddings
        from vector_store.embed import client
        try:
            bulk_collection = client.get_or_create_collection("formd_bulk")
            embedding_count = bulk_collection.count()

            if embedding_count > 200000:  # We have substantial embeddings (292K+)
                logging.info(f"STEP 3b: Skipped bulk embedding - {embedding_count:,} embeddings already exist, using database for context")
                step_results["step3b"] = True
            else:
                logging.info("STEP 3b: upsert_bulk_entries")
                upsert_bulk_entries(data_dir="data/raw")
                logging.info("STEP 3b COMPLETE")
                step_results["step3b"] = True
        except Exception as vector_error:
            logging.warning(f"Could not check vector store: {vector_error}")
            # Skip bulk embeddings and rely on database
            logging.info("STEP 3b: Skipped bulk embedding (using database for historical context)")
            step_results["step3b"] = True
    except Exception:
        logging.exception("Error in STEP 3b")
        step_results["step3b"] = False
        # Continue execution instead of exiting

    # STEP 4: Process entries through MCP
    if use_mcp:
        try:
            if entries:  # Only proceed if we have entries
                logging.info("STEP 4: process_entries_with_mcp")

                # Initialize MCP with Phase 3 parameters
                mcp = ModelControlPoint(
                    data_dir="data",
                    relevance_threshold=relevance_threshold,
                    email_threshold=email_threshold,
                    screening_threshold=screening_threshold,
                    use_multi_stage=use_multi_stage,
                    use_prompt_evaluation=use_prompt_evaluation,
                    use_specialized_analysis=use_specialized_analysis
                )

                # Process entries
                processed_entries = mcp.process_new_filings(
                    feed_entries=entries,
                    historical_context=True
                )

                if not processed_entries:
                    logging.warning("No entries were processed by MCP")

                logging.info("STEP 4 COMPLETE")
                step_results["step4"] = True
            else:
                logging.warning("STEP 4: Skipped (no entries from step 1)")
                step_results["step4"] = False
        except Exception:
            logging.exception("Error in STEP 4")
            step_results["step4"] = False
            # Continue execution instead of exiting
    else:
        # Legacy processing (for backward compatibility)
        try:
            if entries:  # Only proceed if we have entries
                logging.info("STEP 4: rank_entries (legacy)")
                from rank.scorer import rank_entries
                processed_entries = rank_entries(entries)
                if not processed_entries:
                    logging.warning("No entries were ranked")
                logging.info("STEP 4 COMPLETE")
                step_results["step4"] = True
            else:
                logging.warning("STEP 4: Skipped (no entries from step 1)")
                step_results["step4"] = False
        except Exception:
            logging.exception("Error in STEP 4")
            step_results["step4"] = False
            # Continue execution instead of exiting

    # STEP 5: Generate visualizations
    visualization_data = None
    if use_mcp:
        try:
            if processed_entries:  # Only proceed if we have processed entries
                logging.info("STEP 5: generate_visualizations")

                # Generate visualizations using MCP
                visualization_data = mcp.generate_visualizations(processed_entries)

                logging.info("STEP 5 COMPLETE")
                step_results["step5"] = True
            else:
                logging.warning("STEP 5: Skipped (no processed entries from step 4)")
                step_results["step5"] = False
        except Exception:
            logging.exception("Error in STEP 5")
            step_results["step5"] = False
            # Continue execution instead of exiting
    else:
        # Legacy visualization (for backward compatibility)
        try:
            if processed_entries:  # Only proceed if we have processed entries
                logging.info("STEP 5: summarise_heatmap & build HTML (legacy)")
                from rank.summarise import summarise_heatmap
                from emailer.build_html import build_heatmap_html

                narrative = summarise_heatmap(processed_entries)
                html = build_heatmap_html(processed_entries, narrative)

                visualization_data = {
                    "html": html,
                    "narrative": narrative
                }

                logging.info("STEP 5 COMPLETE")
                step_results["step5"] = True
            else:
                logging.warning("STEP 5: Skipped (no processed entries from step 4)")
                step_results["step5"] = False
        except Exception:
            logging.exception("Error in STEP 5")
            step_results["step5"] = False
            # Continue execution instead of exiting

    # STEP 6: Send email
    if use_mcp:
        try:
            if processed_entries and visualization_data:
                logging.info("STEP 6: send_email_alert")

                # Check if email should be sent
                if mcp.should_send_email(processed_entries):
                    # Get email content
                    email_content = mcp.get_email_content(
                        processed_entries=processed_entries,
                        visualization_data=visualization_data
                    )

                    # Send email
                    send_email(
                        subject=email_content["subject"],
                        html_content=email_content["body_html"]
                    )
                    logging.info("STEP 6 COMPLETE - Email sent")
                else:
                    logging.info("STEP 6 COMPLETE - No email needed (threshold not met)")

                step_results["step6"] = True
            else:
                logging.warning("STEP 6: Skipped (missing data from previous steps)")
                step_results["step6"] = False
        except Exception:
            logging.exception("Error in STEP 6")
            step_results["step6"] = False
            # Continue execution instead of exiting
    else:
        # Legacy email sending (for backward compatibility)
        try:
            if visualization_data and "html" in visualization_data:
                logging.info("STEP 6: send_email (legacy)")
                send_email(
                    subject="Private-Market Heat Map — Weekly Brief",
                    html_content=visualization_data["html"]
                )
                logging.info("STEP 6 COMPLETE")
                step_results["step6"] = True
            else:
                logging.warning("STEP 6: Skipped (no HTML content from step 5)")
                step_results["step6"] = False
        except Exception:
            logging.exception("Error in STEP 6")
            step_results["step6"] = False
            # Continue execution instead of exiting

    # Summarize results
    success_count = sum(1 for result in step_results.values() if result)
    total_steps = len(step_results)

    if success_count == total_steps:
        print("✅ run_all completed successfully.")
        logging.info("run_all completed successfully.")
    else:
        print(f"⚠️ run_all completed with {total_steps - success_count} failed steps.")
        logging.warning(f"run_all completed with {total_steps - success_count} failed steps.")
        for step, success in step_results.items():
            status = "✅" if success else "❌"
            print(f"{status} {step}")
            if not success:
                logging.warning(f"Failed step: {step}")

def run_continuous(
    poll_interval: int = 15,
    use_mcp: bool = True,
    relevance_threshold: float = 0.7,
    email_threshold: float = 0.8,
    screening_threshold: float = 0.3,
    skip_zip: bool = False,
    use_multi_stage: bool = True,
    use_prompt_evaluation: bool = True,
    use_specialized_analysis: bool = True
):
    """
    Run the pipeline in continuous mode, polling the SEC ATOM feed at regular intervals.

    Args:
        poll_interval: Minutes between polls
        use_mcp: Whether to use the Model Control Point
        relevance_threshold: Threshold for considering a filing relevant
        email_threshold: Threshold for triggering email alerts
        screening_threshold: Threshold for proceeding from screening to detailed analysis
        skip_zip: Whether to skip ZIP downloads and use placeholders
        use_multi_stage: Whether to use multi-stage analysis pipeline
        use_prompt_evaluation: Whether to use prompt evaluation and improvement
        use_specialized_analysis: Whether to use specialized industry/offering analysis
    """
    logging.info(f"Starting continuous mode (poll interval: {poll_interval} minutes)")

    while True:
        try:
            # Run the full pipeline
            logging.info("Running full pipeline")
            run_all(
                use_mcp=use_mcp,
                relevance_threshold=relevance_threshold,
                email_threshold=email_threshold,
                screening_threshold=screening_threshold,
                skip_zip=skip_zip,
                use_multi_stage=use_multi_stage,
                use_prompt_evaluation=use_prompt_evaluation,
                use_specialized_analysis=use_specialized_analysis
            )

            # Wait for next interval
            next_run = dt.datetime.now() + dt.timedelta(minutes=poll_interval)
            logging.info(f"Next run scheduled for {next_run.strftime('%Y-%m-%d %H:%M:%S')}")
            print(f"Next run scheduled for {next_run.strftime('%Y-%m-%d %H:%M:%S')}")

            # Sleep until next run
            time.sleep(poll_interval * 60)

        except KeyboardInterrupt:
            logging.info("Keyboard interrupt received. Exiting.")
            print("Keyboard interrupt received. Exiting.")
            break

        except Exception as e:
            logging.error(f"Error in continuous mode: {e}")
            print(f"Error in continuous mode: {e}")
            # Wait a shorter time before retry on error
            time.sleep(60)

    logging.info("Continuous mode stopped")

if __name__ == "__main__":
    # Parse command line arguments
    parser = argparse.ArgumentParser(description="Run the SEC Form D analysis pipeline")
    parser.add_argument("--legacy", action="store_true", help="Use legacy pipeline instead of MCP")
    parser.add_argument("--relevance-threshold", type=float, default=0.7,
                       help="Threshold for considering a filing relevant (0.0-1.0)")
    parser.add_argument("--email-threshold", type=float, default=0.8,
                       help="Threshold for triggering email alerts (0.0-1.0)")
    parser.add_argument("--screening-threshold", type=float, default=0.3,
                       help="Threshold for proceeding from screening to detailed analysis (0.0-1.0)")
    parser.add_argument("--continuous", action="store_true",
                       help="Run in continuous mode, polling at regular intervals")
    parser.add_argument("--poll-interval", type=int, default=15,
                       help="Minutes between polls in continuous mode")
    parser.add_argument("--skip-zip", action="store_true",
                       help="Skip ZIP downloads and use placeholders for faster development")

    # Phase 3 feature flags
    parser.add_argument("--disable-multi-stage", action="store_true",
                       help="Disable multi-stage analysis pipeline")
    parser.add_argument("--disable-prompt-evaluation", action="store_true",
                       help="Disable prompt evaluation and improvement")
    parser.add_argument("--disable-specialized-analysis", action="store_true",
                       help="Disable specialized industry/offering analysis")

    args = parser.parse_args()

    if args.continuous:
        # Run in continuous mode
        run_continuous(
            poll_interval=args.poll_interval,
            use_mcp=not args.legacy,
            relevance_threshold=args.relevance_threshold,
            email_threshold=args.email_threshold,
            screening_threshold=args.screening_threshold,
            skip_zip=args.skip_zip,
            use_multi_stage=not args.disable_multi_stage,
            use_prompt_evaluation=not args.disable_prompt_evaluation,
            use_specialized_analysis=not args.disable_specialized_analysis
        )
    else:
        # Run once
        run_all(
            use_mcp=not args.legacy,
            relevance_threshold=args.relevance_threshold,
            email_threshold=args.email_threshold,
            screening_threshold=args.screening_threshold,
            skip_zip=args.skip_zip,
            use_multi_stage=not args.disable_multi_stage,
            use_prompt_evaluation=not args.disable_prompt_evaluation,
            use_specialized_analysis=not args.disable_specialized_analysis
        )

-- db/schema.sql
-- Schema for the Form D data storage system

-- ZIP files table to track downloaded ZIP files
CREATE TABLE IF NOT EXISTS zip_files (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    filename TEXT NOT NULL,
    url TEXT NOT NULL,
    date_str TEXT NOT NULL,  -- YYYYMMDD format
    download_date TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    file_size INTEGER,
    status TEXT NOT NULL DEFAULT 'downloaded',  -- downloaded, extracted, processed, error
    error_message TEXT,
    last_accessed TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    access_count INTEGER NOT NULL DEFAULT 1,
    is_placeholder BOOLEAN NOT NULL DEFAULT 0,
    UNIQUE(filename)
);

-- Form D filings table
CREATE TABLE IF NOT EXISTS form_d_filings (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    accession_number TEXT NOT NULL,
    file_number TEXT,
    issuer_name TEXT NOT NULL,
    filing_date TEXT NOT NULL,
    offering_amount REAL,
    industry_group TEXT,
    issuer_city TEXT,
    issuer_state TEXT,
    issuer_zip TEXT,
    offering_type TEXT,
    minimum_investment REAL,
    total_amount_sold REAL,
    total_remaining REAL,
    source_zip_id INTEGER,
    source_file TEXT,
    json_data TEXT NOT NULL,  -- Full JSON data
    embedding_id TEXT,  -- ID in the vector store
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(accession_number),
    FOREIGN KEY(source_zip_id) REFERENCES zip_files(id)
);

-- Feed entries table
CREATE TABLE IF NOT EXISTS feed_entries (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    entry_id TEXT NOT NULL,
    title TEXT NOT NULL,
    link TEXT NOT NULL,
    published TEXT NOT NULL,
    updated TEXT NOT NULL,
    summary TEXT,
    content TEXT,
    filing_id INTEGER,
    embedding_id TEXT,  -- ID in the vector store
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(entry_id),
    FOREIGN KEY(filing_id) REFERENCES form_d_filings(id)
);

-- Analysis results table
CREATE TABLE IF NOT EXISTS analysis_results (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    filing_id INTEGER NOT NULL,
    relevance_score REAL NOT NULL,
    summary TEXT,
    email_draft TEXT,
    analysis_timestamp TEXT NOT NULL,
    model_name TEXT,
    prompt_used TEXT,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY(filing_id) REFERENCES form_d_filings(id)
);

-- Cache management table
CREATE TABLE IF NOT EXISTS cache_management (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    cache_type TEXT NOT NULL,  -- zip, json, embedding
    item_id INTEGER NOT NULL,  -- reference to the item in its respective table
    last_accessed TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    access_count INTEGER NOT NULL DEFAULT 1,
    priority INTEGER NOT NULL DEFAULT 0,  -- Higher number = higher priority
    size_bytes INTEGER,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_zip_files_date ON zip_files(date_str);
CREATE INDEX IF NOT EXISTS idx_zip_files_status ON zip_files(status);
CREATE INDEX IF NOT EXISTS idx_zip_files_last_accessed ON zip_files(last_accessed);

CREATE INDEX IF NOT EXISTS idx_form_d_filings_date ON form_d_filings(filing_date);
CREATE INDEX IF NOT EXISTS idx_form_d_filings_issuer ON form_d_filings(issuer_name);
CREATE INDEX IF NOT EXISTS idx_form_d_filings_source_zip ON form_d_filings(source_zip_id);

CREATE INDEX IF NOT EXISTS idx_feed_entries_published ON feed_entries(published);
CREATE INDEX IF NOT EXISTS idx_feed_entries_filing_id ON feed_entries(filing_id);

CREATE INDEX IF NOT EXISTS idx_analysis_results_filing_id ON analysis_results(filing_id);
CREATE INDEX IF NOT EXISTS idx_analysis_results_relevance ON analysis_results(relevance_score);

CREATE INDEX IF NOT EXISTS idx_cache_management_type ON cache_management(cache_type);
CREATE INDEX IF NOT EXISTS idx_cache_management_last_accessed ON cache_management(last_accessed);
CREATE INDEX IF NOT EXISTS idx_cache_management_priority ON cache_management(priority);
